# This file contains example configuration for <PERSON><PERSON><PERSON>ii<PERSON>.
# Copy it to .env, read thoroughly and update to match your environment.


# Server listening address (defaults to localhost; set to 0.0.0.0 by Dockerfiles)
#HOST=0.0.0.0
# Server port
PORT=3000


# Database settings

# Choose mysql or postgres
# NOTE: MySQL will not be supported by Ilmomasiina 3.0.
DB_DIALECT=<mysql|postgres>
DB_HOST=localhost
#DB_PORT=<3306|5432>
DB_USER=ilmo_user
DB_PASSWORD=password
DB_DATABASE=ilmomasiina
#DB_SSL=false
#DEBUG_DB_LOGGING=false


# Time limit settings

# How long each user has to edit their signup after creation.
SIGNUP_CONFIRM_MINS=30
# Whether signups can be edited for SIGNUP_CONFIRM_MINS after creation, even if
# signups for the event have closed.
# It's recommended to enable this to improve the user experience, but it's off
# by default for backwards compatibility.
SIGNUP_CONFIRM_AFTER_CLOSE=true

# Privacy-related settings

# How long after an event's date to remove signup details.
ANONYMIZE_AFTER_DAYS=180

# How long after an event's date it will become fully invisible to users.
HIDE_EVENT_AFTER_DAYS=180

# How long items stay in the database after deletion, in order to allow restoring
# accidentally deleted items.
DELETION_GRACE_PERIOD_DAYS=14


# Whether or not to trust X-Forwarded-For headers for remote IP. Set to true IF
# AND ONLY IF running behind a proxy that sets this header.
TRUST_PROXY=false


# Authentication secrets

# Set both of these to different secure random strings.
# You can generate one with the command:
# openssl rand -hex 32
NEW_EDIT_TOKEN_SECRET=
FEATHERS_AUTH_SECRET=

# If migrating from an Athene version of Ilmomasiina, copy this setting over to
# allow old links to work. Otherwise, leave this empty.
EDIT_TOKEN_SALT=


# Time zone for both frontend and backend.
APP_TIMEZONE=Europe/Helsinki


# Mail settings

# Mail sender
MAIL_FROM=<<EMAIL>>

# Default mail language. Mostly useful for the "promoted from queue" emails when they were created before
# a version supporting language detection.
MAIL_DEFAULT_LANG=fi

# If you want to send emails, choose either SMTP or Mailgun.
# In development, you can omit both and emails will be printed to the console.

# SMTP server host and credentials, if using direct SMTP.
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASSWORD=
SMTP_TLS=false

# Mailgun API key and domain, if using it.
MAILGUN_API_KEY=
MAILGUN_DOMAIN=
# Mailgun server to use (defaults to api.eu.mailgun.net)
MAILGUN_HOST=api.eu.mailgun.net


# URL settings

# Canonical base URL for the app. Used by the backend.
# Include $PATH_PREFIX, but NOT a final "/".
# e.g. "http://example.com" or "http://example.com/ilmo"
BASE_URL=http://localhost:3000

# Domain name to use for iCalendar UIDs. Should be kept constant.
# Defaults to the domain of BASE_URL.
#ICAL_UID_DOMAIN=

# URI prefix for the app. Used for frontend URLs.
# Include initial "/", but NOT a final "/".
# e.g. "" or "/ilmo"
PATH_PREFIX=

# URI prefix or full base URL to the API. Used by the frontend.
# Leave empty to use "$PATH_PREFIX/api".
# YOU SHOULD LEAVE THIS EMPTY unless you're building the frontend against a remote API.
# Include "/api" if applicable but NOT a final "/".
API_URL=

# URL template for an event details page. Used by the backend for iCalendar exports.
# Leave empty to use the default routes used by the frontend, i.e. "$BASE_URL/events/{id}".
# YOU SHOULD LEAVE THIS EMPTY unless you're using a customized frontend with different paths.
# Use the token {slug}, e.g. http://example.com/event/{slug}
# You can also use {lang}.

#EVENT_DETAILS_URL=

# URL template for a signup edit page. Used by the backend for emails.
# Leave empty to use the default routes used by the frontend, i.e. "$BASE_URL/signup/{id}/{editToken}".
# YOU SHOULD LEAVE THIS UNSET unless you're using a customized frontend with different paths.
# Use the tokens {id} and {editToken}, e.g. http://example.com/signup/{id}/{editToken}
# You can also use {lang}.

#EDIT_SIGNUP_URL=

# URL template for the admin main page. Used by the backend for emails.
# Leave empty to use the default routes used by the frontend, i.e. "$BASE_URL/admin".
# YOU SHOULD LEAVE THIS UNSET unless you're using a customized frontend with different paths.
# You can use the token {lang}, e.g. http://example.com/{lang}/admin

#ADMIN_URL=

# Allowed origins for cross-site requests to API. Separate with commas or use * for all.
ALLOW_ORIGIN=


# Sentry.io public DSN for error tracking (only used in production, leave empty to disable)
SENTRY_DSN=


# Branding settings

# Website strings (requires website rebuild)
BRANDING_HEADER_TITLE_TEXT=Kilta ry ilmomasiina
BRANDING_HEADER_TITLE_TEXT_SHORT=Ilmomasiina
BRANDING_FOOTER_GDPR_TEXT=Tietosuoja
BRANDING_FOOTER_GDPR_LINK=http://example.com/privacy
BRANDING_FOOTER_HOME_TEXT=Example.com
BRANDING_FOOTER_HOME_LINK=http://example.com
BRANDING_LOGIN_PLACEHOLDER_EMAIL=<EMAIL>

# Email strings
BRANDING_MAIL_FOOTER_TEXT=Rakkaudella, Tietskarijengi & Athene
BRANDING_MAIL_FOOTER_LINK=https://ilmo.athene.fi

# iCalendar exported calendar name
BRANDING_ICAL_CALENDAR_NAME=Ilmomasiina
