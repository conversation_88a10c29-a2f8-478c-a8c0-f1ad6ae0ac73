{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "2.0.0-alpha41", "private": true, "license": "MIT", "engines": {"node": "^20"}, "packageManager": "pnpm@9.1.1", "scripts": {"bootstrap": "pnpm install", "clean": "pnpm run -r clean", "build": "pnpm run -r --workspace-concurrency=1 build", "start": "pnpm run -r --parallel start", "lint": "eslint packages", "lint:fix": "npm run lint -- --fix", "format": "prettier -w packages/*/{src,test}/**/*.{ts,tsx,scss}", "typecheck": "pnpm run -r --workspace-concurrency=1 typecheck", "test": "pnpm run -r test", "test:backend": "pnpm run --filter ilmomasiina-backend test"}, "repository": {"type": "git", "url": "git+https://github.com/Tietokilta/ilmomasiina"}, "bugs": {"url": "https://github.com/Tietokilta/ilmomasiina/issues"}, "dependencies": {"@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.57.1", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-promise": "^6.6.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-simple-import-sort": "^12.1.1", "prettier": "^3.5.3", "typescript": "~5.2.2"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "babel": {"presets": ["react-app"]}, "pnpm": {"peerDependencyRules": {"ignoreMissing": ["j<PERSON>y", "popper.js", "eslint"]}, "overrides": {"@types/react": "^17"}}}