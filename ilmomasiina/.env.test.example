# This file contains the bare minimum settings to test Ilmomasiina.
# Database settings are not set here, as your setup for that may vary.


# Database settings

# Choose mysql or postgres
# NOTE: MySQL will not be supported by Ilmomasiina 3.0.
#DB_DIALECT=<mysql|postgres>
#DB_HOST=localhost
#DB_PORT=<3306|5432>
#DB_USER=ilmo_user
#DB_PASSWORD=password
#DB_DATABASE=ilmomasiina
#DB_SSL=false


# Authentication secrets

# Set both of these to different secure random strings.
# You can generate one with the command:
# openssl rand -hex 32
NEW_EDIT_TOKEN_SECRET=insecure_but_that_is_ok_in_testing_1
FEATHERS_AUTH_SECRET=insecure_but_that_is_ok_in_testing_2


# Mail settings

# Mail sender
MAIL_FROM=<<EMAIL>>


# URL settings

# Canonical base URL for the app. Used by the backend.
# Include $PATH_PREFIX, but NOT a final "/".
# e.g. "http://example.com" or "http://example.com/ilmo"
BASE_URL=http://localhost:3000


# Branding settings

# Website strings (requires website rebuild)
BRANDING_HEADER_TITLE_TEXT=Ilmomasiina
BRANDING_FOOTER_GDPR_TEXT=Tietosuoja
BRANDING_FOOTER_GDPR_LINK=http://example.com/privacy
BRANDING_FOOTER_HOME_TEXT=Example.com
BRANDING_FOOTER_HOME_LINK=http://example.com
BRANDING_LOGIN_PLACEHOLDER_EMAIL=<EMAIL>

# Email strings
BRANDING_MAIL_FOOTER_TEXT=Ilmomasiina test email footer
BRANDING_MAIL_FOOTER_LINK=https://example.com

# iCalendar exported calendar name
BRANDING_ICAL_CALENDAR_NAME=Ilmomasiina
