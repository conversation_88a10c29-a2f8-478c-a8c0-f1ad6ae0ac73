# This compose file is for development use only – Do not use this in production!

# This compose file builds Ilmomasiina and serves it at http://localhost:3000.
# For easier development, it provides hot reloading by utilizing bind mounts.
# You will still need to rebuild if you update the project's dependencies.

# Env vars from the .env file (in repository root) will be used by docker-compose and passed to the containers.
# Remember to create your own .env file (see .env.example)!
# The database will use the DB_USER, DB_PASSWORD and DB_DATABASE provided by you.

services:
  database:
    image: postgres:latest
    restart: always
    environment:
      - POSTGRES_PASSWORD=$DB_PASSWORD
      - POSTGRES_USER=$DB_USER
      - POSTGRES_DB=$DB_DATABASE
    volumes:
      - ./data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
  ilmomasiina-dev:
    build:
      context: .
      dockerfile: "Dockerfile.dev"
    command: "npm start"
    restart: always
    depends_on:
      - database
    environment:
      - DB_DIALECT=postgres
      - DB_HOST=database
      - NODE_ENV=development
      - HOST=0.0.0.0
    volumes:
      - type: bind
        source: ./packages/ilmomasiina-models/src
        target: /opt/ilmomasiina/packages/ilmomasiina-models/src
      - type: bind
        source: ./packages/ilmomasiina-frontend/public
        target: /opt/ilmomasiina/packages/ilmomasiina-frontend/public
      - type: bind
        source: ./packages/ilmomasiina-frontend/src
        target: /opt/ilmomasiina/packages/ilmomasiina-frontend/src
      - type: bind
        source: ./packages/ilmomasiina-frontend/vite.config.ts
        target: /opt/ilmomasiina/packages/ilmomasiina-frontend/vite.config.ts
      - type: bind
        source: ./packages/ilmomasiina-frontend/index.html
        target: /opt/ilmomasiina/packages/ilmomasiina-frontend/index.html
      - type: bind
        source: ./packages/ilmomasiina-components/src
        target: /opt/ilmomasiina/packages/ilmomasiina-components/src
      - type: bind
        source: ./packages/ilmomasiina-backend/src
        target: /opt/ilmomasiina/packages/ilmomasiina-backend/src
      - type: bind
        source: ./packages/ilmomasiina-backend/emails
        target: /opt/ilmomasiina/packages/ilmomasiina-backend/emails
      - type: bind
        source: .env
        target: /opt/ilmomasiina/.env
    ports:
      - "3000:3000"
