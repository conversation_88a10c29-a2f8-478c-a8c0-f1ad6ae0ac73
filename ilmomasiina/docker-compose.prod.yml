version: '3.2'

# This compose file demonstrates how <PERSON><PERSON><PERSON><PERSON><PERSON> could be run in a production environment.

# This compose file serves <PERSON><PERSON><PERSON><PERSON><PERSON> at http://localhost:8000.
# Env variables from .env file (in the repository root) will be used by docker compose and passed to the container.
# Remember to create your own .env file (see .env.example)!

services:
  database:
    image: postgres:latest
    restart: always
    environment:
      - POSTGRES_PASSWORD=$DB_PASSWORD
      - POSTGRES_USER=$DB_USER
      - POSTGRES_DB=$DB_DATABASE

  ilmomasiina:
    build:
      context: .
      dockerfile: "Dockerfile"
      args:
        #- PATH_PREFIX=
        #- API_URL=
        - BRANDING_HEADER_TITLE_TEXT=Kilta ry ilmomasiina
        - BRANDING_HEADER_TITLE_TEXT_SHORT=Ilmomasiina
        - BRANDING_FOOTER_GDPR_TEXT=Tietosuoja
        - BRANDING_FOOTER_GDPR_LINK=http://example.com/privacy
        - BRANDING_FOOTER_HOME_TEXT=Example.com
        - BRANDING_FOOTER_HOME_LINK=http://example.com
        - BRANDING_LOGIN_PLACEHOLDER_EMAIL=<EMAIL>
    restart: always
    depends_on:
      - database
    environment:
      - DB_HOST=database
      - NODE_ENV=production
      - ENFORCE_HTTPS=false
      - PORT=3000
      # When deploying to production, either set ENFORCE_HTTPS true (default) or
      # configure the reverse proxy to enforce HTTPS communication with the clients.
    env_file:
      - .env
    ports:
      - "8000:3000"
#    volumes:
      #- "frontend:/opt/ilmomasiina/frontend"

#  nginx:
#    image: nginx:latest
#    ports:
#      - "8888:80"
#    volumes:
#      - "frontend:/usr/share/nginx/html"

#volumes:
#  frontend:
    # Contains static files of the ilmomasiina-frontend.
    # To achieve better performance under high loads, these files
    # can be served using a more performant web server or even a CDN.
