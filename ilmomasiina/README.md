# Ilmomasiina

Ilmomasiina is the event registration system originally created by Athene in 2016,
and forked in 2021 by Tietokilta for our new website.
There are [numerous major upgrades](./CHANGELOG.md) from the Athene version.

Ilmomasiina is easy to [install](docs/installation.md) in various environments and
is freely available for all organizations to use.
[Migrating](docs/migration.md) from the Athene-made version is also supported.

The latest reviewed development version is in the `dev` branch. You may want to check
current PRs for somewhat stable feature additions.

**Please note that the code is currently in alpha phase and may still have bugs.**
Major features for 2.0 are mostly done and we expect to reach a stable beta in spring 2024.

## Installation

See [installation.md](docs/installation.md).

## Documentation

See the [documentation](docs/README.md) for more information.

## For developers

Progress and planning is tracked in GitHub issues.
Please see and update the [project board](https://github.com/Tietokilta/ilmomasiina/projects/1) for ongoing work.

All help is appreciated. Please contact @PurkkaKoodari or another member of Tietokilta's Digitoimikunta if you wish to
actively help with development &ndash; there are still major changes to be done that may conflict with yours.
Start by reading the [docs](docs/README.md) to get familiar with the project.
