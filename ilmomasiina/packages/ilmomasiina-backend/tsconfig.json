{"compilerOptions": {"target": "ES2019", "lib": ["ES2019", "dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "CommonJS", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "incremental": true, "rootDirs": ["src", "test", "."], "outDir": "dist", "tsBuildInfoFile": "dist/tsconfig.tsbuildinfo", "baseUrl": ".", "paths": {"@tietokilta/ilmomasiina-models": ["../ilmomasiina-models/src/index.ts"], "@tietokilta/ilmomasiina-models/dist/*": ["../ilmomasiina-models/src/*"]}}, "exclude": ["node_modules", "dist"], "include": ["src/**/*", "test/**/*", "vitest.config.ts"], "references": [{"path": "../ilmomasiina-models"}]}