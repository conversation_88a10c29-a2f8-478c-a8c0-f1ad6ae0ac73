extends ../layout.pug

block mainContent
  if admin && type == "signup"
    div.content-block
      p.bodyText #[strong Ylläpitäjä on ilmoittanut sinut tapahtumaan.]
  if admin && type == "edit"
    div.content-block
      p.bodyText #[strong Ylläpitäjä on muokannut ilmoittautumistasi.]
  if event.verificationEmail
    div.content-block
      p.bodyText #{event.verificationEmail}
  if queuePosition
    div.content-block
      p.bodyText #[strong Olet jonossa sijalla #{queuePosition}.] Jo<PERSON> saat paikan jonosta, sinulle lähetetään ilmoitus sähköpostitse.
  div.content-block
    p.bodyText Tapahtuman tiedot:
    ul
      li #[strong Tapahtuma:] #{event.title}
      li #[strong Sijainti:] #{event.location}
      if date
        li #[strong Aika:] #{date}
  div.content-block
    p.bodyText Ilmoittautumisesi tiedot:
    ul
      if name
        li #[strong Nimi:] #{name}
      li #[strong Sähköposti:] #{email}
      li #[strong Kiintiö:] #{quota}
      each val in answers
        li #[strong #{val.label}:] #{val.answer}
  div.content-block
    p.bodyText Mikäli haluat muokata tai perua ilmoittautumisesi, voit tehdä sen painamalla tätä #[a(href=cancelLink) linkkiä].
