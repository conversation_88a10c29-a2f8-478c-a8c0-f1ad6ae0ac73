extends ../layout.pug

block mainContent
  if admin && type == "signup"
    div.content-block
      p.bodyText #[strong An administrator has signed you up to this event.]
  if admin && type == "edit"
    div.content-block
      p.bodyText #[strong An administrator has edited your signup.]
  if event.verificationEmail
    div.content-block
      p.bodyText #{event.verificationEmail}
  if queuePosition
    div.content-block
      p.bodyText #[strong You are in the queue in position #{queuePosition}.] If you get accepted from the queue, you will be notified by email.
  div.content-block
    p.bodyText Event details:
    ul
      li #[strong Event:] #{event.title}
      li #[strong Location:] #{event.location}
      if date
        li #[strong Time:] #{date}
  div.content-block
    p.bodyText Signup details:
    ul
      if name
        li #[strong Name:] #{name}
      li #[strong Email:] #{email}
      li #[strong Quota:] #{quota}
      each val in answers
        li #[strong #{val.label}:] #{val.answer}
  div.content-block
    p.bodyText If you want to edit or cancel your signup, you can do it by clicking #[a(href=cancelLink) this link].
