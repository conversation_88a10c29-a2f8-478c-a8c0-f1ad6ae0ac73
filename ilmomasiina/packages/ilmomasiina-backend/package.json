{"name": "@tietokilta/ilmomasiina-backend", "version": "2.0.0-alpha41", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/Tietokilta/ilmomasiina.git"}, "scripts": {"start": "bnr start:dev", "start:dev": "bnr start:dev", "start:prod": "pnpm build && bnr start:prod", "start:bench": "pnpm build && bnr start:bench", "loadTest": "k6 run loadTest.js", "stop:prod": "pm2 stop 0", "monit:prod": "pm2 monit", "build": "tsc --build tsconfig.build.json", "clean": "<PERSON><PERSON><PERSON> dist", "typecheck": "tsc --build tsconfig.build.json", "test": "vitest", "filldb": "ts-node -r tsconfig-paths/register --project tsconfig.json test/fillDatabase.ts"}, "betterScripts": {"start:dev": {"command": "ts-node-dev -r tsconfig-paths/register --respawn --project tsconfig.json src/bin/server.ts", "env": {"NODE_ENV": "development", "DEBUG": "app:*", "PORT": "3001"}}, "start:prod": {"command": "pm2 start dist/bin/server.js", "env": {"NODE_ENV": "production", "DEBUG": "app:*"}}, "start:bench": {"command": "node dist/bin/server.js", "env": {"NODE_ENV": "bench", "DEBUG": "app:*"}}}, "dependencies": {"@fastify/compress": "^7.0.3", "@fastify/cors": "^9.0.1", "@fastify/sensible": "^5.6.0", "@fastify/static": "^7.0.4", "@fastify/type-provider-typebox": "^4.1.0", "@sinclair/typebox": "^0.32.35", "@tietokilta/ilmomasiina-models": "workspace:*", "@types/validator": "^13.15.1", "ajv": "^8.17.1", "ajv-formats": "^2.1.1", "base32-encode": "^1.2.0", "bcrypt": "^5.1.1", "better-npm-run": "^0.1.1", "debug": "^4.4.1", "dotenv-flow": "^4.1.0", "email-templates": "^11.1.1", "fast-jwt": "^4.0.5", "fastify": "^4.29.1", "http-errors": "^2.0.0", "i18next": "^23.16.8", "ics": "^3.8.1", "lodash": "^4.17.21", "moment": "^2.30.1", "moment-timezone": "^0.5.48", "mysql2": "^3.14.1", "node-cron": "^3.0.3", "nodemailer": "^6.10.1", "nodemailer-mailgun-transport": "^2.1.5", "pg": "^8.16.0", "pg-hstore": "^2.3.4", "sequelize": "^6.37.7", "umzug": "^3.8.2", "validator": "^13.15.15"}, "devDependencies": {"@faker-js/faker": "^8.4.1", "@types/bcrypt": "^5.0.2", "@types/compression": "^1.8.1", "@types/debug": "^4.1.12", "@types/email-templates": "^10.0.4", "@types/http-errors": "^2.0.5", "@types/k6": "^0.51.1", "@types/lodash": "^4.17.17", "@types/node": "^20.19.0", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.17", "@types/nodemailer-mailgun-transport": "^1.4.6", "rimraf": "^5.0.10", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "tsconfig-paths": "^4.2.0", "typescript": "~5.2.2", "vite-tsconfig-paths": "^4.3.2", "vitest": "^1.6.1"}}