{"name": "@tietokilta/ilmomasiina-models", "version": "2.0.0-alpha41", "repository": {"type": "git", "url": "git+https://github.com/Tietokilta/ilmomasiina.git"}, "license": "MIT", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "node": "./dist/index.js", "import": "./dist/index.js"}, "./dist/*": {"types": "./dist/*.d.ts", "import": "./dist/*.js", "node": "./dist/*.js"}, "./src/*": "./src/*.ts"}, "scripts": {"build": "tsc --build", "clean": "<PERSON><PERSON><PERSON> dist", "typecheck": "tsc --build"}, "devDependencies": {"rimraf": "^5.0.10", "typescript": "~5.2.2"}, "dependencies": {"@sinclair/typebox": "^0.32.35"}}