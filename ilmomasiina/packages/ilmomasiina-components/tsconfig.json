{"compilerOptions": {"target": "esnext", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "CommonJS", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "react", "incremental": true, "declaration": true, "declarationMap": true, "rootDir": "src", "outDir": "dist", "tsBuildInfoFile": "dist/tsconfig.tsbuildinfo", "composite": true, "baseUrl": ".", "paths": {"@tietokilta/ilmomasiina-models": ["../ilmomasiina-models/src/index.ts"]}}, "exclude": ["node_modules", "dist"], "include": ["src", "src/locales/*.json"], "references": [{"path": "../ilmomasiina-models"}]}