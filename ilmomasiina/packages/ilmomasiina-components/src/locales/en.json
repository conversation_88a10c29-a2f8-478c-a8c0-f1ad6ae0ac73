{"components": {"dateFormat.locale": "en-FI", "duration.days_one": "{{count}} day", "duration.days_other": "{{count}} days", "duration.hours_one": "{{count}} hour", "duration.hours_other": "{{count}} hours", "duration.mins_one": "{{count}} minute", "duration.mins_other": "{{count}} minutes", "duration.secs_one": "{{count}} second", "duration.secs_other": "{{count}} seconds", "editSignup.backToEvent": "Back to event page", "editSignup.action.cancel": "Cancel", "editSignup.action.edit": "Update", "editSignup.action.save": "Save", "editSignup.action.back": "Back", "editSignup.delete.action": "Delete signup", "editSignup.delete.action.confirm": "<PERSON><PERSON> u<PERSON> var<PERSON>…", "editSignup.delete.info1": "Are you sure you want to delete your sign up to <1>{{event}}</1>?", "editSignup.delete.info2": "If you delete your signup you will lose your spot in the queue. If you change your mind you can always sign up again to the event later, but you will be moved to the end of the queue.", "editSignup.delete.info3": "This cannot be undone.", "editSignup.editInstructions.email": "The link will also be included in the confirmation email.", "editSignup.editInstructions": "You can edit or delete your signup later by saving the URL of this page.", "editSignup.loadError.BadEditToken.title": "Invalid verification token", "editSignup.loadError.BadEditToken.description": "The verification token in the URL is incorrect. Please verify that you typed the URL correctly.", "editSignup.loadError.404.title": "Signup not found", "editSignup.loadError.404.description": "Your signup was not found. It might be already deleted, or you might have mistyped the URL.", "editSignup.loadError.default.description": "Your signup could not be loaded.", "editSignup.fields.email": "Email", "editSignup.fields.email.placeholder": "Email", "editSignup.fields.firstName": "First name", "editSignup.fields.firstName.placeholder": "First name", "editSignup.fields.lastName": "Last name", "editSignup.fields.lastName.placeholder": "Last name", "editSignup.fields.select.placeholder": "Choose…", "editSignup.namePublic": "Show name in public signup list", "editSignup.position.openQuota": "You are in the open quota in position {{position}}.", "editSignup.position.queue": "You are in the queue in position {{position}}.", "editSignup.position.quota": "You are in quota {{quota}} in position {{position}}.", "editSignup.editable.unconfirmed": "Your signup is still valid for {{duration}}. Save the form to make sure it doesn't expire.", "editSignup.editable.confirmed": "Your signup can still be edited for: {{duration}}", "editSignup.editable.closed": "Your signup cannot be changed anymore as the signup for the event has closed.", "editSignup.publicQuestion": "Answers to this field are public.", "editSignup.status.delete": "Deleting signup", "editSignup.status.deleteSuccess": "Your signup was deleted successfully.", "editSignup.status.edit": "Updating signup", "editSignup.status.editSuccess": "Editing succeeded", "editSignup.status.signup": "Saving signup", "editSignup.status.signupSuccess": "<PERSON><PERSON> succeeded", "editSignup.deleteError.SignupsClosed.description": "The signup for this event has closed, so your signup could no longer be deleted.", "editSignup.deleteError.NoSuchSignup.description": "Your signup has already been deleted.", "editSignup.deleteError.default.description": "Deletion failed", "editSignup.editError.SignupsClosed.description": "The signup for this event has closed, so your changes could no longer be saved.", "editSignup.editError.NoSuchSignup.description": "Your signup was not found. It might be already deleted.", "editSignup.editError.400.description": "Editing failed. Make sure you have filled all mandatory fields and try again.", "editSignup.editError.default.description": "Editing failed.", "editSignup.signupError.SignupsClosed.description": "The signup for this event has closed, so your signup could no longer be saved.", "editSignup.signupError.NoSuchSignup.description": "Your signup was not found. It might be already deleted.", "editSignup.signupError.400.description": "Signup failed. Make sure you have filled all mandatory fields and try again.", "editSignup.signupError.default.description": "Signup failed.", "editSignup.fieldError.missing": "This field is mandatory.", "editSignup.fieldError.wrongType": "The answer to this field is of the wrong type. Try refreshing the page.", "editSignup.fieldError.tooLong": "The answer to this field is too long.", "editSignup.fieldError.invalidEmail": "The email address is invalid.", "editSignup.fieldError.notANumber": "The answer to this question must be a valid number.", "editSignup.fieldError.notAnOption": "The answer to this question isn't in the allowed options. Try refreshing the page.", "editSignup.fieldError": "<PERSON><PERSON><PERSON> on virheellinen. Kokeile päivittää sivu.", "editSignup.title.edit": "Edit signup", "editSignup.title.signup": "Sign up", "editSignup.title.preview": "Preview signup form", "errors.returnToEvents": "Return to event list", "errors.contactAdmin": "If the problem persists, please report it to the administrators.", "errors.default.title": "Whoops, something went wrong", "errors.InitialSetupNeeded.title": "Initial setup needed", "errors.InitialSetupNeeded.description": "The server seems to be freshly installed. This frontend doesn't support initial setup – please visit the admin interface to set up the server.", "errors.500.description": "An internal error occurred on the server. $t(errors.contactAdmin)", "errors.502.description": "The backend server seems to be down. $t(errors.contactAdmin)", "errors.503.description": "The backend server seems to be down. $t(errors.contactAdmin)", "errors.504.description": "The backend server seems to be unresponsive. $t(errors.contactAdmin)", "errors.default.description": "An unknown error occurred. $t(errors.contactAdmin)", "events.column.date": "Date", "events.column.name": "Name", "events.column.signupCount": "Signups", "events.column.signupStatus": "Signup", "events.loadError.default.description": "Failed to load events. $t(errors.contactAdmin)", "events.openQuota": "Open", "events.signupCount": "Signups:", "events.title": "Events", "signupState.disabled": "You cannot sign up for this event.", "signupState.notOpened": "Signup opens on {{date}}.", "signupState.notOpened.short": "Opens on {{date}}.", "signupState.open": "Signup open until {{date}}.", "signupState.open.short": "Until {{date}}.", "signupState.closed": "The signup for this event has closed at {{date}}.", "signupState.closed.short": "Closed on {{date}}.", "singleEvent.editEvent": "Edit", "singleEvent.info.category": "Category:", "singleEvent.info.date": "Date:", "singleEvent.info.endDate": "Ends:", "singleEvent.info.facebookEvent": "Facebook event:", "singleEvent.info.location": "Location:", "singleEvent.info.price": "Price:", "singleEvent.info.startDate": "Starts:", "singleEvent.info.website": "Website:", "singleEvent.loadError.404.title": "Event not found", "singleEvent.loadError.404.description": "The event was not found. It may be too far in the past or deleted, or you might have mistyped the URL.", "singleEvent.loadError.default.description": "Failed to load event details. $t(errors.contactAdmin)", "singleEvent.quotaCounts.openQuota": "Open quota", "singleEvent.quotaCounts.queue": "In queue: {{count}}", "singleEvent.quotaCounts.title": "Signups", "singleEvent.quotaCounts.unlimited": "Unlimited", "singleEvent.returnToEvents": "Back", "singleEvent.signupButton.singleQuota": "Sign up now", "singleEvent.signupButton": "Sign up: {{quota}}", "singleEvent.signupButton.preview": "Preview signup form", "singleEvent.signupButtons.title": "Sign up", "singleEvent.signups.emptyQuota": "No signups.", "singleEvent.signups.quotaTitle.in-open": "Open quota", "singleEvent.signups.quotaTitle.in-queue": "In queue", "singleEvent.signups.nameHidden": "Hidden", "singleEvent.signups.position": "Position", "singleEvent.signups.name": "Name", "singleEvent.signups.quota": "<PERSON><PERSON><PERSON>", "singleEvent.signups.signupTime": "Signup time", "singleEvent.signups.title": "Signups", "singleEvent.signups.unconfirmed": "Unconfirmed", "singleEvent.signupInProgress": "Signup in progress", "singleEvent.signupError.NoSuchQuota.description": "The quota you chose has been deleted.", "singleEvent.signupError.SignupsClosed.description": "The signup for this event has closed.", "singleEvent.signupError.default.description": "Signup creation failed."}}