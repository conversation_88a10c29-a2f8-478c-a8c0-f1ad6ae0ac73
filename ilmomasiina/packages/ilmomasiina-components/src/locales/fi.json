{"components": {"dateFormat.locale": "fi-FI", "duration.days_one": "{{count}} p<PERSON><PERSON><PERSON>", "duration.days_other": "{{count}} p<PERSON><PERSON><PERSON><PERSON>", "duration.hours_one": "{{count}} tunti", "duration.hours_other": "{{count}} tuntia", "duration.mins_one": "{{count}} minu<PERSON>i", "duration.mins_other": "{{count}} minu<PERSON>ia", "duration.secs_one": "{{count}} sekunti", "duration.secs_other": "{{count}} sekuntia", "editSignup.backToEvent": "<PERSON><PERSON><PERSON>", "editSignup.action.cancel": "Peruuta", "editSignup.action.edit": "Päivitä", "editSignup.action.save": "<PERSON><PERSON><PERSON>", "editSignup.action.back": "<PERSON><PERSON><PERSON>", "editSignup.delete.action": "<PERSON>ista il<PERSON>", "editSignup.delete.action.confirm": "<PERSON><PERSON> u<PERSON> var<PERSON>…", "editSignup.delete.info1": "<PERSON><PERSON><PERSON>, ett<PERSON> haluat poistaa ilmoittautumisesi tapah<PERSON>aan <1>{{event}}</1>?", "editSignup.delete.info2": "Jos poistat <PERSON>, menet<PERSON> paikkasi jonossa. Jos muutat <PERSON>, voit aina ilmoittautua tapah<PERSON> uude<PERSON>, mutta siir<PERSON>t silloin jonon hänn<PERSON>.", "editSignup.delete.info3": "Tätä toimintoa ei voi perua.", "editSignup.editInstructions.email": "Linkki lähetetään myös sähköpostiisi vahvistusviestissä.", "editSignup.editInstructions": "Voit muokata ilmoittautumistasi tai poistaa sen my<PERSON><PERSON>min tallentamalla tämän sivun URL-osoitteen.", "editSignup.loadError.BadEditToken.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "editSignup.loadError.BadEditToken.description": "URL-osoitteen vahvistuskoodi on virheellinen. Tarkista, että kirjoitit URL-osoitteen oikein.", "editSignup.loadError.404.title": "Ilmoittautumista ei löytynyt", "editSignup.loadError.404.description": "Ilmoittautumistasi ei lö<PERSON>. Se saattaa olla jo poistettu.", "editSignup.loadError.default.description": "Ilmoittautumisen lataaminen epäonnistui. $t(errors.contactAdmin)", "editSignup.fields.email": "Sähköposti", "editSignup.fields.email.placeholder": "Sähköposti", "editSignup.fields.firstName": "<PERSON><PERSON><PERSON><PERSON>", "editSignup.fields.firstName.placeholder": "<PERSON><PERSON><PERSON><PERSON>", "editSignup.fields.lastName": "<PERSON><PERSON><PERSON><PERSON>", "editSignup.fields.lastName.placeholder": "<PERSON><PERSON><PERSON><PERSON>", "editSignup.fields.select.placeholder": "Valitse…", "editSignup.namePublic": "Näytä nimi jul<PERSON><PERSON>a <PERSON>u<PERSON>a", "editSignup.position.openQuota": "<PERSON>t avoimessa kiintiössä sijalla {{position}}.", "editSignup.position.queue": "<PERSON><PERSON> j<PERSON> si<PERSON> {{position}}.", "editSignup.position.quota": "<PERSON><PERSON> {{quota}} sijalla {{position}}.", "editSignup.editable.unconfirmed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on voimassa vielä {{duration}}. <PERSON><PERSON><PERSON>, jotta se ei vanhene.", "editSignup.editable.confirmed": "Muokkausaikaa j<PERSON>ljellä: {{duration}}", "editSignup.editable.closed": "Ilmoittautumistasi ei voi enää muokata tai perua, koska tapah<PERSON>an ilmoittautuminen on sulkeutunut.", "editSignup.publicQuestion": "Tämän kentän vastaukset ovat julkisia.", "editSignup.status.delete": "Ilmoittautumista p<PERSON>", "editSignup.status.deleteSuccess": "Ilmoittautumisesi poistettiin onnistuneesti.", "editSignup.status.edit": "Tallennetaan mu<PERSON>ia", "editSignup.status.editSuccess": "<PERSON><PERSON><PERSON><PERSON>", "editSignup.status.signup": "<PERSON><PERSON><PERSON><PERSON>umi<PERSON>", "editSignup.status.signupSuccess": "Il<PERSON><PERSON><PERSON><PERSON><PERSON>", "editSignup.deleteError.SignupsClosed.description": "Tapahtuman ilmoittautuminen on sulkeutunut, joten ilmoittautumistasi ei voitu enää poistaa.", "editSignup.deleteError.NoSuchSignup.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on jo poistettu.", "editSignup.deleteError.default.description": "<PERSON><PERSON><PERSON>.", "editSignup.editError.SignupsClosed.description": "Tapahtuman ilmoittautuminen on sulkeutunut, joten muutoksiasi ei voitu enää tallentaa.", "editSignup.editError.NoSuchSignup.description": "Ilmoittautumistasi ei lö<PERSON>. Se saattaa olla jo poistettu.", "editSignup.editError.400.description": "Muokkaus epäonnistui. Tarkista, että kaikki pakolliset kentät on täytetty ja yritä uudes<PERSON>.", "editSignup.editError.default.description": "<PERSON><PERSON><PERSON><PERSON> e<PERSON>.", "editSignup.signupError.SignupsClosed.description": "Tapahtuman ilmoittautuminen on sulkeutunut, joten ilmoittautumistasi ei voitu enää tallentaa.", "editSignup.signupError.NoSuchSignup.description": "Ilmoittautumistasi ei lö<PERSON>. Se saattaa olla jo poistettu.", "editSignup.signupError.400.description": "Ilmoittautuminen epäonnistui. Tarkista, että kaikki pakolliset kentät on täytetty ja yritä uudes<PERSON>.", "editSignup.signupError.default.description": "Ilmoittautuminen epäonnistui.", "editSignup.fieldError.missing": "Tämä kenttä on pakollinen.", "editSignup.fieldError.wrongType": "<PERSON><PERSON><PERSON> vast<PERSON> on väärää tyyppiä. Kokeile päivittää sivu.", "editSignup.fieldError.tooLong": "<PERSON><PERSON><PERSON> on liian pitk<PERSON>.", "editSignup.fieldError.invalidEmail": "Sähköpostiosoite on virheellinen.", "editSignup.fieldError.notANumber": "<PERSON><PERSON><PERSON> tulee olla numero.", "editSignup.fieldError.notAnOption": "<PERSON><PERSON><PERSON> vastaus ei ole sallituissa vaihtoehdoissa. Kokeile päivittää sivu.", "editSignup.fieldError": "<PERSON><PERSON><PERSON> on virheellinen. Kokeile päivittää sivu.", "editSignup.title.edit": "Muok<PERSON>a ilmoittautumista", "editSignup.title.signup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "editSignup.title.preview": "Esikatsele ilmolomaketta", "errors.returnToEvents": "<PERSON><PERSON><PERSON>", "errors.contactAdmin": "<PERSON><PERSON>, ota yht<PERSON> y<PERSON>pit<PERSON>.", "errors.default.title": "<PERSON><PERSON>, jotain meni pieleen", "errors.default.description": "Tuntematon virhe. $t(errors.contactAdmin)", "errors.InitialSetupNeeded.title": "Käyttöönotto kesken", "errors.InitialSetupNeeded.description": "Palvelin vaikuttaa vasta asennetulta. Tämä käyttöliittymä ei tue käyttöönottoa – ota palvelin käyttöön hallintakäyttöliittymän kautta.", "errors.500.description": "Palvelimella tapahtui virhe. $t(errors.contactAdmin)", "errors.502.description": "Taustapalvelimeen ei saada yhteyttä. $t(errors.contactAdmin)", "errors.503.description": "Taustapalvelimeen ei saada yhteyttä. $t(errors.contactAdmin)", "errors.504.description": "Taustapalvelin ei vastaa pyyntöihin. $t(errors.contactAdmin)", "events.column.date": "<PERSON><PERSON><PERSON><PERSON>", "events.column.name": "<PERSON><PERSON>", "events.column.signupCount": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "events.column.signupStatus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "events.loadError.default.description": "Tapahtumien lataaminen epäonnistui. $t(errors.contactAdmin)", "events.openQuota": "Avoin", "events.signupCount": "Il<PERSON>itt<PERSON><PERSON><PERSON>:", "events.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "signupState.disabled": "Tapahtumaan ei voi ilmoittautua.", "signupState.notOpened": "Ilmoittautuminen alkaa {{date}}.", "signupState.notOpened.short": "Alkaa {{date}}.", "signupState.open": "Il<PERSON>ittaut<PERSON><PERSON> auki {{date}} asti.", "signupState.open.short": "<PERSON><PERSON> {{date}} asti.", "signupState.closed": "Ilmoittautuminen päättyi {{date}}.", "signupState.closed.short": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{date}}.", "singleEvent.editEvent": "<PERSON><PERSON><PERSON><PERSON>", "singleEvent.info.category": "Kategoria:", "singleEvent.info.date": "<PERSON><PERSON><PERSON><PERSON>:", "singleEvent.info.endDate": "Loppuu:", "singleEvent.info.facebookEvent": "Facebook-tapahtuma:", "singleEvent.info.location": "Sijainti:", "singleEvent.info.price": "<PERSON>nta:", "singleEvent.info.startDate": "Alkaa:", "singleEvent.info.website": "Kotisivut:", "singleEvent.loadError.404.title": "Tapahtumaa ei lö<PERSON>yt", "singleEvent.loadError.404.description": "Tapahtumaa ei lö<PERSON>yt. Se saattaa olla menneisyydessä tai poistettu, tai olet ehkä kirjoittanut URL-osoitteen väärin.", "singleEvent.loadError.default.description": "Tapahtuman lataaminen epäonnistui. $t(errors.contactAdmin)", "singleEvent.quotaCounts.openQuota": "<PERSON><PERSON>in kii<PERSON>", "singleEvent.quotaCounts.queue": "Jonossa: {{count}}", "singleEvent.quotaCounts.title": "Ilmoittautuneet", "singleEvent.quotaCounts.unlimited": "<PERSON><PERSON>", "singleEvent.returnToEvents": "<PERSON><PERSON><PERSON>", "singleEvent.signupButton.singleQuota": "<PERSON><PERSON><PERSON><PERSON><PERSON> nyt", "singleEvent.signupButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>: {{quota}}", "singleEvent.signupButton.preview": "Esikatsele ilmolomaketta", "singleEvent.signupButtons.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "singleEvent.signups.emptyQuota": "Ei ilmoittautumisia.", "singleEvent.signups.quotaTitle.in-open": "<PERSON><PERSON>in kii<PERSON>", "singleEvent.signups.quotaTitle.in-queue": "<PERSON><PERSON>", "singleEvent.signups.nameHidden": "Piilotettu", "singleEvent.signups.position": "<PERSON><PERSON>", "singleEvent.signups.name": "<PERSON><PERSON>", "singleEvent.signups.quota": "Kiintiö", "singleEvent.signups.signupTime": "Ilmoittautumisaika", "singleEvent.signups.title": "Ilmoittautuneet", "singleEvent.signups.unconfirmed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "singleEvent.signupInProgress": "Ilmoittautuminen käynnissä", "singleEvent.signupError.NoSuchQuota.description": "Valitsemasi kiintiö on poistettu.", "singleEvent.signupError.SignupsClosed.description": "Tapa<PERSON><PERSON> ilmoittautuminen on sulkeutunut.", "singleEvent.signupError.default.description": "Ilmoittautuminen epäonnistui."}}