@use "sass:color";

// Override theme colors for Bootstrap.
// These are defined before the imports below for use in Ilmomasiina's SCSS.

// For Bootstrap components, the overrides are made in ilmomasiina-frontend/src/styles/app.scss.
// If you want to override more colors, you'll also need to add them to the @use there.

$primary: #0a0d10 !default;
$secondary: #0a0d10 !default;
$red: #d74949 !default; // further assigned to $danger by Bootstrap
$green: #319236 !default; // further assigned to $success by Bootstrap
$text-muted: #888 !default;

// Links are impossible to see with Tietokilta's black $primary.
// You can disable this if you use something light.
$force-link-underline: true !default;

// Default button hover color logic doesn't work with Tietokilta's black $primary.
// You can disable this if you use something light.
$lighter-primary-hover: true !default;
$lighter-secondary-hover: true !default;

// Additional colors, only used by Ilmomasiina's SCSS.

$secondary-background: #f1f1f1 !default;
$secondary-text-color: #7a7a7a !default;

// If you don't want a header logo, set this to false.
$header-logo: true !default;

// Import Bootstrap core mixins/variables/functions for use in Ilmomasiina's SCSS.

@import "bootstrap/scss/functions";
@import "bootstrap/scss/variables";
@import "bootstrap/scss/mixins";

// Colors for signup states. You can use Bootstrap variables here.

$signup-state-not-opened: $body-color !default;
$signup-state-opened: $green !default;
$signup-state-closed: $red !default;
$signup-state-disabled: color.change($body-color, $alpha: 0.45) !default;
