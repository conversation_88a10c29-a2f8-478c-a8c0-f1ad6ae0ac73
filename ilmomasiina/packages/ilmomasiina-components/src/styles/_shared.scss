@use "definitions" as *;
@use "forms";

@include media-breakpoint-up(sm) {
  .ilmo--mobile-only {
    display: none;
  }
}
@include media-breakpoint-down(xs) {
  .ilmo--desktop-only {
    display: none;
  }
}

.ilmo--title-nav {
  display: flex;
  align-items: center;
  column-gap: $spacer * 0.5;

  :first-child {
    flex: 1 1 auto;
    min-width: 0;
  }

  // on narrow screens, always wrap the button(s)
  @include media-breakpoint-down(xs) {
    flex-wrap: wrap;
    padding-bottom: $spacer;

    :first-child {
      flex: 1 0 100%;
    }
  }
}

.ilmo--narrow-container {
  justify-content: center;
}
.ilmo--status-container {
  text-align: center;
}
.ilmo--loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 50px;
}

@if $lighter-primary-hover {
  .btn-primary {
    @include button-variant(
      $primary,
      $primary,
      $hover-background: lighten($primary, 15%),
      $hover-border: lighten($primary, 20%),
      $active-background: lighten($primary, 20%),
      $active-border: lighten($primary, 25%)
    );
  }
}
@if $lighter-secondary-hover {
  .btn-secondary {
    @include button-variant(
      $primary,
      $primary,
      $hover-background: lighten($primary, 15%),
      $hover-border: lighten($primary, 20%),
      $active-background: lighten($primary, 20%),
      $active-border: lighten($primary, 25%)
    );
  }
}
