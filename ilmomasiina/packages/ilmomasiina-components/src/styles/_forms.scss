@use "definitions" as *;

.ilmo--form {
  // Make forms look nicer when labels wrap in two column view
  .form-group.row {
    margin-bottom: 0;

    @include media-breakpoint-up(sm) {
      > :last-child {
        margin-bottom: 1rem;
      }
    }
  }

  label {
    font-weight: 600;
  }

  label[data-required]:not([data-required="false"])::after {
    content: "\00A0*";
    color: $red;
    font-weight: 600;
  }

  label.form-check-label {
    font-weight: 400;
  }

  .ilmo--label-column {
    @include media-breakpoint-up(sm) {
      text-align: right;
    }
    // Make empty labels not consume the height of a text line in single column view
    @include media-breakpoint-down(xs) {
      label:empty {
        display: block;
      }
    }
  }

  textarea {
    max-width: 100%;
  }

  .btn[disabled] {
    opacity: 0.35;
  }

  // We use custom feedback elements instead of the ones provided by react-bootstrap, as the
  // latter don't work with checkbox/radio arrays.
  .invalid-feedback {
    display: none !important;
  }
}
