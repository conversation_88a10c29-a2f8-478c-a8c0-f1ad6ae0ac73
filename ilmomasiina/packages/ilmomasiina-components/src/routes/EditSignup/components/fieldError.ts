import { useCallback } from "react";

import { DefaultNamespace, Parse<PERSON><PERSON><PERSON> } from "i18next";
import { useTranslation } from "react-i18next";

import { SignupFieldError } from "@tietokilta/ilmomasiina-models";

/** Localizes a SignupFieldError. */
export default function useFieldErrors() {
  const { t } = useTranslation();
  return useCallback(
    (error?: SignupFieldError): string[] | string | undefined =>
      error ? t([`editSignup.fieldError.${error}` as Parse<PERSON><PERSON>s<DefaultNamespace>, "editSignup.fieldError"]) : undefined,
    [t],
  );
}
