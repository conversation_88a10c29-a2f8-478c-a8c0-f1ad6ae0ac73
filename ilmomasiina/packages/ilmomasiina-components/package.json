{"name": "@tietokilta/ilmomasiina-components", "version": "2.0.0-alpha41", "repository": {"type": "git", "url": "git+https://github.com/Tietokilta/ilmomasiina.git"}, "license": "MIT", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./dist/*": {"types": "./dist/*.d.ts", "import": "./dist/*.js"}, "./src/*": "./src/*.ts"}, "scripts": {"build": "tsc --build", "clean": "<PERSON><PERSON><PERSON> dist", "typecheck": "tsc --build"}, "dependencies": {"@tietokilta/ilmomasiina-models": "workspace:*", "bootstrap": "^4.6.2", "final-form": "^4.20.10", "i18next": "^23.16.8", "lodash-es": "^4.17.21", "react": "^17 || ^18.3.1", "react-bootstrap": "^1.6.8", "react-countdown": "^2.3.6", "react-dom": "^17 || ^18.3.1", "react-final-form": "^6.5.9", "react-i18next": "^14.1.3", "react-markdown": "^8.0.7", "react-toastify": "^9.1.3", "remark-gfm": "^3.0.1"}, "devDependencies": {"@types/lodash-es": "^4.17.12", "@types/react": "^17.0.87", "rimraf": "^5.0.10", "typescript": "~5.2.2"}}