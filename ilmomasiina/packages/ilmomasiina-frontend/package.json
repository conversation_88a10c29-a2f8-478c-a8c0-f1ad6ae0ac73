{"name": "@tietokilta/ilmomasiina-frontend", "version": "2.0.0-alpha41", "scripts": {"build": "pnpm typecheck && vite build", "clean": "<PERSON><PERSON>f build dist", "start": "vite", "typecheck": "tsc --build"}, "repository": {"type": "git", "url": "git+https://github.com/Tietokilta/ilmomasiina.git"}, "license": "MIT", "dependencies": {"@redux-devtools/extension": "^3.3.0", "@sentry/browser": "^9.27.0", "@tietokilta/ilmomasiina-components": "workspace:*", "@tietokilta/ilmomasiina-models": "workspace:*", "bootstrap": "^4.6.2", "connected-react-router": "^6.9.3", "csv-stringify": "^6.5.2", "date-fns": "^2.30.0", "dotenv-flow": "^4.1.0", "final-form": "^4.20.10", "final-form-arrays": "^3.1.0", "history": "^4.10.1", "i18next": "^23.16.8", "i18next-browser-languagedetector": "^7.2.2", "lodash-es": "^4.17.21", "react": "^17.0.2", "react-bootstrap": "^1.6.8", "react-datepicker": "^4.25.0", "react-dom": "^17.0.2", "react-final-form": "^6.5.9", "react-final-form-arrays": "^3.1.4", "react-i18next": "^14.1.3", "react-redux": "^7.2.9", "react-router": "^5.3.4", "react-router-dom": "^5.3.4", "react-sortable-hoc": "^2.0.0", "react-toastify": "^9.1.3", "react-widgets": "^5.8.6", "redux": "^4.2.1", "redux-persist": "^6.0.0", "redux-thunk": "^2.4.2", "reselect": "^4.1.8", "sass": "^1.89.2", "zod": "^3.25.56"}, "devDependencies": {"@types/history": "^4.7.11", "@types/lodash-es": "^4.17.12", "@types/node": "^20.19.0", "@types/node-sass": "^4.11.8", "@types/react": "^17.0.87", "@types/react-datepicker": "^4.19.6", "@types/react-dom": "^17.0.26", "@types/react-redux": "^7.1.34", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.5.1", "rimraf": "^5.0.10", "ts-node": "^10.9.2", "typescript": "~5.2.2", "vite": "^5.4.19", "vite-plugin-checker": "^0.6.4", "vite-tsconfig-paths": "^4.3.2"}}