{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "react", "incremental": true, "outDir": "dist", "rootDirs": ["src", "."], "baseUrl": ".", "paths": {"@tietokilta/ilmomasiina-components": ["../ilmomasiina-components/src/index.ts"], "@tietokilta/ilmomasiina-components/dist/*": ["../ilmomasiina-components/src/*"], "@tietokilta/ilmomasiina-models": ["../ilmomasiina-models/src/index.ts"]}}, "exclude": ["dist"], "include": ["src", "vite.config.ts"], "references": [{"path": "../ilmomasiina-components"}, {"path": "../ilmomasiina-models"}]}