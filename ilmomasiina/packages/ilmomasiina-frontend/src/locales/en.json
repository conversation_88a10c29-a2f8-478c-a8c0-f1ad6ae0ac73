{"frontend": {"errors.404.title": "404", "errors.404.description": "The page was not found.", "errors.returnToEvents": "Return to event list", "datePicker.time": "Time", "header.switchLanguage": "In English", "header.logout": "Logout", "footer.admin": "Admin", "auth.loginExpired": "Your session has expired. Please log in again.", "auth.loginSuccess": "Logged in successfully.", "auth.logoutSuccess": "Logged out successfully.", "login.title": "Log in", "login.email": "Email", "login.password": "Password", "login.submit": "Log in", "login.errors.401.description": "Wrong username or password", "login.errors.default.description": "<PERSON><PERSON> failed", "adminEvents.title": "Manage events", "adminEvents.title.past": "Past events", "adminEvents.loadError.default.description": "Failed to load events. $t(errors.contactAdmin)", "adminEvents.nav.users": "Users", "adminEvents.nav.auditLog": "Audit log", "adminEvents.nav.newEvent": "+ New Event", "adminEvents.nav.past": "Past events", "adminEvents.nav.upcoming": "Upcoming events", "adminEvents.noEvents.past": "No past events to show. Would you like to <1>view upcoming events</1> or <3>create a new event</3>?", "adminEvents.noEvents.upcoming": "No upcoming events to show. Would you like to <1>view past events</1> or <3>create a new event</3>?", "adminEvents.column.name": "Name", "adminEvents.column.date": "Date", "adminEvents.column.status": "Status", "adminEvents.column.signups": "Signups", "adminEvents.column.actions": "Actions", "adminEvents.status.draft": "Draft", "adminEvents.status.closed": "Closed", "adminEvents.status.ended": "In past", "adminEvents.status.hidden": "Hidden", "adminEvents.status.published": "Published", "adminEvents.action.edit": "Edit event", "adminEvents.action.delete": "Delete event", "adminEvents.action.delete.confirm": "Are you sure you want to delete this event? This cannot be undone.", "adminEvents.action.delete.error.default.description": "Failed to delete event.", "adminUsers.loadError.default.description": "Failed to load users. $t(errors.contactAdmin)", "adminUsers.title": "User management", "adminUsers.column.email": "Email", "adminUsers.column.actions": "Actions", "adminUsers.deleteUser": "Delete user", "adminUsers.deleteUser.confirm": "Are you sure you want to delete {{user}}? This cannot be undone.", "adminUsers.deleteUser.success": "User {{user}} deleted successfully.", "adminUsers.deleteUser.errors.CannotDeleteSelf.description": "You can't delete your own user.", "adminUsers.deleteUser.errors.404.description": "The user {{user}} has already been deleted.", "adminUsers.deleteUser.errors.default.description": "Failed to delete {{user}}.", "adminUsers.resetPassword": "Reset password", "adminUsers.resetPassword.confirm": "Are you sure you want to reset the password for {{user}}? The new password will be sent to their email address.", "adminUsers.resetPassword.success": "The password for {{user}} was successfully reset.", "adminUsers.resetPassword.errors.404.description": "The user {{user}} no longer exists.", "adminUsers.resetPassword.errors.default.description": "Failed to reset password for {{user}}.", "adminUsers.createUser": "Create new user", "adminUsers.createUser.email": "Email", "adminUsers.createUser.passwordInfo": "The new user will receive their password via email.", "adminUsers.createUser.submit": "Create new user", "adminUsers.createUser.success": "User {{email}} created successfully.", "adminUsers.createUser.errors.409.description": "A user already exists with the email {{email}}.", "adminUsers.createUser.errors.default.description": "Creating the user failed.", "adminUsers.changePassword": "Change your password", "adminUsers.changePassword.oldPassword": "Old password", "adminUsers.changePassword.newPassword": "New password", "adminUsers.changePassword.newPasswordVerify": "New password again", "adminUsers.changePassword.submit": "Change password", "adminUsers.changePassword.errors.required": "This field is required", "adminUsers.changePassword.errors.minLength": "The password must have at least {{number}} characters", "adminUsers.changePassword.errors.verifyMatch": "The passwords do not match", "adminUsers.changePassword.success": "Password changed successfully.", "adminUsers.changePassword.errors.WrongOldPassword.description": "Failed to change password.", "adminUsers.changePassword.errors.default.description": "Old password is incorrect.", "adminUsers.returnToEvents": "Go back", "initialSetup.title": "Create admin user", "initialSetup.welcome1": "Welcome to Ilmomasiina!", "initialSetup.welcome2": "Start by creating the first admin user. You can create more admin users later via the admin panel.", "initialSetup.email": "Email", "initialSetup.password": "New password", "initialSetup.passwordVerify": "New password again", "initialSetup.submit": "Create user", "initialSetup.errors.required": "This field is required", "initialSetup.errors.minLength": "The password must have at least {{number}} characters", "initialSetup.errors.verifyMatch": "The passwords do not match", "initialSetup.success": "User created successfully.", "initialSetup.errors.InitialSetupAlreadyDone.description": "The initial user has already been created. Please log in with an existing user.", "initialSetup.errors.default.description": "Creating the user failed.", "auditLog.title": "Audit log", "auditLog.loadError.default.description": "Failed to load logs. $t(errors.contactAdmin)", "auditLog.returnToEvents": "Go back", "auditLog.column.time": "Time", "auditLog.column.user": "User", "auditLog.column.ipAddress": "IP address", "auditLog.column.action": "Action", "auditLog.filter": "Filter…", "auditLog.filter.action": "Action…", "auditLog.filter.event": "Event…", "auditLog.filter.signup": "Signup…", "auditLog.filter.action.createEvent": "Event: Create", "auditLog.filter.action.editEvent": "Event: Edit", "auditLog.filter.action.publishEvent": "Event: Publish", "auditLog.filter.action.unpublishEvent": "Event: Convert to draft", "auditLog.filter.action.deleteEvent": "Event: Delete", "auditLog.filter.action.createSignup": "Signup: Create", "auditLog.filter.action.editSignup": "Signup: Edit", "auditLog.filter.action.deleteSignup": "Signup: Delete", "auditLog.filter.action.promoteSignup": "Signup: Accepted from queue", "auditLog.filter.action.createUser": "User: Create", "auditLog.filter.action.deleteUser": "User: Delete", "auditLog.filter.action.resetPassword": "User: Reset password", "auditLog.filter.action.changeOwnPassword": "User: Change own password", "auditLog.pagination": "Rows <1 />–{{last}} out of {{total}}", "auditLog.pagination.previous": "Previous page", "auditLog.pagination.next": "Next page", "auditLog.description.createEvent": "created event <1>{{event}}</1>", "auditLog.description.editEvent": "edited event <1>{{event}}</1>", "auditLog.description.publishEvent": "published event <1>{{event}}</1>", "auditLog.description.unpublishEvent": "converted event <1>{{event}}</1> back to draft", "auditLog.description.deleteEvent": "deleted event <1>{{event}}</1>", "auditLog.description.createSignup": "created signup {{signup}} in event <3>{{event}}</3>", "auditLog.description.editSignup": "edited signup {{signup}} in event <3>{{event}}</3>", "auditLog.description.deleteSignup": "deleted signup {{signup}} in event <3>{{event}}</3>", "auditLog.description.promoteSignup": "signup {{signup}} was accepted from queue in event <3>{{event}}</3>", "auditLog.description.createUser": "created user {{user}}", "auditLog.description.deleteUser": "deleted user {{user}}", "auditLog.description.resetPassword": "reset the password for user {{user}}", "auditLog.description.changeOwnPassword": "changed their own password", "auditLog.description.unknown": "unknown action {{action}}", "editor.loadError.404.title": "Event not found", "editor.loadError.404.description": "An event with id {{eventId}} was not found. It may be deleted.", "editor.loadError.default.description": "Failed to load event. $t(errors.contactAdmin)", "editor.title.edit": "Edit event", "editor.title.new": "Create event", "editor.action.goBack": "Go back", "editor.status.draft": "Draft", "editor.status.published": "Published", "editor.status.createSuccess": "Event created successfully!", "editor.status.saveSuccess": "Changes saved successfully!", "editor.saveInvalid": "The event has errors. Correct them and try saving again.", "editor.saveError.default.description": "Failed to save the event.", "editor.action.publish": "Publish", "editor.action.saveDraft": "Save as draft", "editor.action.convertToDraft": "Convert to draft", "editor.action.saveChanges": "Save changes", "editor.tabs.basic": "Basic details", "editor.tabs.quotas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "editor.tabs.questions": "Questions", "editor.tabs.emails": "Confirmation emails", "editor.tabs.preview": "Preview", "editor.tabs.signups": "Signups", "editor.errors.required": "This field is required", "editor.errors.tooShort": "Enter at least {{minimum}} characters", "editor.errors.tooLong": "Enter at most {{maximum}} characters", "editor.errors.tooSmall": "The value must be at least {{minimum}}", "editor.errors.tooLarge": "The value must be at most {{maximum}}", "editor.errors.invalidSlug": "Only the characters A-Z, a-z, 0-9, - and _ are allowed", "editor.errors.optionsTooLong": "The combined answer options are too long", "editor.errors.generic": "Invalid value: {{error}}", "editor.basic.name": "Event name", "editor.basic.startDate": "Starts at", "editor.basic.endDate": "Ends at", "editor.basic.endDate.info": "The event will only appear in calendar exports if it has an ending time.", "editor.basic.url": "Event URL", "editor.basic.url.checking": "Checking availability…", "editor.basic.url.free": "URL available!", "editor.basic.url.reserved": "This URL is already in use for {{event}}", "editor.basic.listed": "Publicity", "editor.basic.listed.check": "Show in event list", "editor.basic.listed.info": "Hidden events are only accessible via URL. Events saved as draft cannot be viewed by users regardless of this setting.", "editor.basic.type": "Event type", "editor.basic.type.onlyEvent": "Event only, no signup", "editor.basic.type.eventWithSignup": "Event with signup", "editor.basic.type.onlySignup": "Signup only, no event", "editor.basic.category": "Category", "editor.basic.homePage": "Website", "editor.basic.facebook": "Facebook event", "editor.basic.location": "Location", "editor.basic.price": "Price", "editor.basic.description": "Description", "editor.basic.description.info": "Markdown can be used in the description.", "editor.basic.registrationStartDate": "Signup opens", "editor.basic.registrationEndDate": "Signup closes", "editor.basic.signupsPublic": "Signup publicity", "editor.basic.signupsPublic.check": "Signups to this event are public", "editor.basic.signupsPublic.info": "The attendee can decide whether their name is publicly visible. Answers to public custom questions are always visible.", "editor.quotas.addQuota": "Add quota", "editor.quotas.deleteQuota": "Delete quota", "editor.quotas.quotaName": "Quota name", "editor.quotas.quotaName.singleQuota": "If there is only one quota, you can e.g. give it the same name as the event.", "editor.quotas.quotaName.reorder": "You can reorder quotas by dragging from the handles on the left.", "editor.quotas.quotaSize": "Quota size", "editor.quotas.quotaSize.info": "If the quota size is unlimited, leave the field empty.", "editor.quotas.quotaSize.unlimited": "unlimited", "editor.quotas.openQuota": "Open quota", "editor.quotas.openQuota.check": "Include an additional open quota", "editor.quotas.openQuota.info": "Signups that did not fit in their own quota will be automatically placed in the open quota, in the order of signups.", "editor.quotas.openQuotaSize": "Open quota size", "editor.questions.nameQuestion": "Name", "editor.questions.nameQuestion.check": "Collect names", "editor.questions.nameQuestion.infoOn": "Name will be a required question. Each participant can decide if their name is publicly visible.", "editor.questions.nameQuestion.infoOff": "If names are collected, each participant can decide if their name is publicly visible.", "editor.questions.emailQuestion": "Email", "editor.questions.emailQuestion.check": "Collect email addresses", "editor.questions.emailQuestion.infoOn": "Email will be a mandatory question. Participants will be sent a confirmation email and an email notification if they got accepted from the queue.", "editor.questions.emailQuestion.infoOff": "If email addresses are not collected, participants will not get a confirmation email nor an email notification if they got accepted from the queue.", "editor.questions.addQuestion": "Add question", "editor.questions.deleteQuestion": "Delete question", "editor.questions.questionText": "Question", "editor.questions.questionType": "Type", "editor.questions.questionType.text": "Text (short)", "editor.questions.questionType.textarea": "Text (long)", "editor.questions.questionType.number": "Number", "editor.questions.questionType.select": "Multiple choice (choose only one)", "editor.questions.questionType.checkbox": "Multiple choice (choose any number)", "editor.questions.questionRequired": "Required", "editor.questions.questionPublic": "Public", "editor.questions.questionOptions": "Answer option", "editor.questions.questionOptions.add": "Add answer option", "editor.questions.questionOptions.delete": "Delete", "editor.emails.verificationEmail": "Confirmation email", "editor.signups.noSignups": "There are currently no signups for this event. When people sign up, they will appear here.", "editor.signups.emptyQuota": "There are currently no signups for this quota. When people sign up, they will appear here.", "editor.signups.download": "Download list of participants", "editor.signups.download.filename": "{{event}} participants.csv", "editor.signups.groupByQuota": "Group by quota", "editor.signups.inQueue": "In queue", "editor.signups.column.firstName": "First name", "editor.signups.column.lastName": "Last name", "editor.signups.column.email": "Email", "editor.signups.column.quota": "<PERSON><PERSON><PERSON>", "editor.signups.column.status.in-open": "Open", "editor.signups.column.status.in-queue": "Queue", "editor.signups.column.time": "Signup time", "editor.signups.column.actions": "Actions", "editor.signups.action.edit": "Edit", "editor.signups.action.delete": "Delete", "editor.signups.action.delete.confirm": "Are you sure? This cannot be undone.", "editor.signups.action.create": "Add signup", "editor.signups.unconfirmed": "Unconfirmed", "editor.editConflict.title": "Conflicting edits", "editor.editConflict.info1": "Another user or tab has edited this event at <1>{{time}}</1>.", "editor.editConflict.info1.withDeleted": "Another user or tab has edited this event at <1>{{time}}</1> and deleted the following quotas or questions:", "editor.editConflict.question": "Question:", "editor.editConflict.quota": "Quota:", "editor.editConflict.info2": "You can save the event and overwrite the other user's changes, or abandon the changes you've made and continue from the event edited by the other user.", "editor.editConflict.action.cancel": "Cancel", "editor.editConflict.action.revert": "Abandon changes", "editor.editConflict.action.overwrite": "Overwrite", "editor.moveToQueue.title": "Move signups to queue?", "editor.moveToQueue.info1": "The changes you're making to quotas would move at least {{number}} people, already accepted into the event, into the queue. These users will not be notified automatically.", "editor.moveToQueue.info2": "Are you sure you want to proceed?", "editor.moveToQueue.action.proceed": "Proceed", "editor.moveToQueue.action.cancel": "Cancel", "editor.editSignup.title.edit": "Edit signup", "editor.editSignup.title.create": "Create signup", "editor.editSignup.quota": "<PERSON><PERSON><PERSON>", "editor.editSignup.sendEmail": "Send email", "editor.editSignup.sendEmail.edit": "Send the participant a confirmation email", "editor.editSignup.sendEmail.create": "Send the participant a confirmation email", "editor.editSignup.keepEditing": "Continue creating", "editor.editSignup.keepEditing.check": "Continue creating signups after saving", "editor.editSignup.validation": "This form bypasses answer validation. Check your entered values carefully.", "editor.editSignup.action.cancel": "Cancel", "editor.editSignup.action.save": "Save", "editor.editSignup.error.NoSuchSignup.description": "The signup was not found. It might be already deleted..", "editor.editSignup.error.default.description": "Saving the signup failed.", "editor.editSignup.success": "Signup saved."}}