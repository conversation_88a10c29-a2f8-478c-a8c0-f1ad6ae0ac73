{"frontend": {"errors.404.title": "404", "errors.404.description": "Sivua ei l<PERSON>.", "errors.returnToEvents": "<PERSON><PERSON><PERSON>", "datePicker.time": "<PERSON><PERSON>", "header.switchLanguage": "<PERSON><PERSON><PERSON><PERSON>", "header.logout": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "footer.admin": "<PERSON><PERSON><PERSON>", "auth.loginExpired": "Sisäänkirjautumisesi on vanhentunut. Kir<PERSON>ud<PERSON> sisään uude<PERSON>.", "auth.loginSuccess": "Sisäänkirjautuminen onnistui.", "auth.logoutSuccess": "Uloskirjautuminen <PERSON>.", "login.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "login.email": "Sähköposti", "login.password": "<PERSON><PERSON><PERSON>", "login.submit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "login.errors.401.description": "Väärä käyttäjänimi tai salasana", "login.errors.default.description": "<PERSON><PERSON><PERSON>ut<PERSON>nen e<PERSON>äonnistui", "adminEvents.title": "<PERSON><PERSON><PERSON>", "adminEvents.title.past": "<PERSON><PERSON><PERSON> tap<PERSON>", "adminEvents.loadError.default.description": "Tapahtumien lataaminen epäonnistui. $t(errors.contactAdmin)", "adminEvents.nav.users": "Käyttäjät", "adminEvents.nav.auditLog": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "adminEvents.nav.newEvent": "+ <PERSON><PERSON><PERSON> tap<PERSON>", "adminEvents.nav.past": "<PERSON><PERSON><PERSON> tap<PERSON>", "adminEvents.nav.upcoming": "Tulevat tapahtumat", "adminEvents.noEvents.past": "Ei menneitä tapahtumia. Haluatko <1>kats<PERSON> tulevia tapahtumia</1> tai <3>luoda uuden tapah<PERSON></3>?", "adminEvents.noEvents.upcoming": "Ei tulevia tapahtumia. Haluatko <1>kats<PERSON> menneit<PERSON> tapahtumia</1> tai <3>luoda uuden tapah<PERSON></3>?", "adminEvents.column.name": "<PERSON><PERSON>", "adminEvents.column.date": "<PERSON><PERSON><PERSON><PERSON>", "adminEvents.column.status": "Tila", "adminEvents.column.signups": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adminEvents.column.actions": "<PERSON><PERSON><PERSON><PERSON>", "adminEvents.status.draft": "Luonnos", "adminEvents.status.closed": "Sulkeutunut", "adminEvents.status.ended": "Men<PERSON><PERSON>", "adminEvents.status.hidden": "Piilotettu", "adminEvents.status.published": "Julkaistu", "adminEvents.action.edit": "<PERSON><PERSON><PERSON><PERSON> tap<PERSON>", "adminEvents.action.delete": "Poista tapah<PERSON>a", "adminEvents.action.delete.confirm": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän tapahtuman? Tätä toimintoa ei voi perua.", "adminEvents.action.delete.error.default.description": "Tapahtuman poistaminen epäonnistui.", "adminUsers.loadError.default.description": "Käyttäjien lataaminen epäonnistui. $t(errors.contactAdmin)", "adminUsers.title": "Käyttäji<PERSON> hallinta", "adminUsers.column.email": "Sähköposti", "adminUsers.column.actions": "<PERSON><PERSON><PERSON><PERSON>", "adminUsers.deleteUser": "Poista käyttäjä", "adminUsers.deleteUser.confirm": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa käyttä<PERSON>än {{user}}? Tätä toimintoa ei voi perua.", "adminUsers.deleteUser.success": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{user}} pois<PERSON><PERSON><PERSON> on<PERSON><PERSON><PERSON>.", "adminUsers.deleteUser.errors.CannotDeleteSelf.description": "Et voi poistaa omaa k<PERSON>täjääsi.", "adminUsers.deleteUser.errors.404.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{user}} on jo poistettu.", "adminUsers.deleteUser.errors.default.description": "K<PERSON><PERSON><PERSON>ä<PERSON><PERSON><PERSON> {{user}} poistaminen epäonnistui.", "adminUsers.resetPassword": "<PERSON><PERSON><PERSON>", "adminUsers.resetPassword.confirm": "<PERSON><PERSON><PERSON><PERSON> varmasti nollata käyttäjän {{user}} salasanan? Uusi salasana lähetetään käyttäjän sähköpostiin.", "adminUsers.resetPassword.success": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{user}} sa<PERSON><PERSON> nolla<PERSON>in onnistuneesti.", "adminUsers.resetPassword.errors.404.description": "Käyttäjää {{user}} ei ole enää olemassa.", "adminUsers.resetPassword.errors.default.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{user}} salasanan nollaaminen epäonnistui.", "adminUsers.createUser": "<PERSON><PERSON> uusi k<PERSON>äj<PERSON>", "adminUsers.createUser.email": "Sähköposti", "adminUsers.createUser.passwordInfo": "Salasana lähetetään uudelle käyttäjälle sähköpostitse.", "adminUsers.createUser.submit": "<PERSON><PERSON> uusi k<PERSON>äj<PERSON>", "adminUsers.createUser.success": "<PERSON><PERSON><PERSON>täj<PERSON> {{email}} luoti<PERSON> onnist<PERSON><PERSON>.", "adminUsers.createUser.errors.409.description": "Sähköpostiosoitteelle {{email}} on jo o<PERSON><PERSON><PERSON> k<PERSON>äj<PERSON>.", "adminUsers.createUser.errors.default.description": "Käyttäjän luominen epäonnistui.", "adminUsers.changePassword": "<PERSON><PERSON><PERSON><PERSON>", "adminUsers.changePassword.oldPassword": "<PERSON><PERSON>", "adminUsers.changePassword.newPassword": "<PERSON><PERSON><PERSON>", "adminUsers.changePassword.newPasswordVerify": "<PERSON><PERSON><PERSON>", "adminUsers.changePassword.submit": "<PERSON><PERSON><PERSON><PERSON>", "adminUsers.changePassword.errors.required": "Pakollinen kenttä", "adminUsers.changePassword.errors.minLength": "Salasanassa täytyy olla vähintään {{number}} merkkiä", "adminUsers.changePassword.errors.verifyMatch": "Salasanat eivät täsmää", "adminUsers.changePassword.success": "<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON><PERSON>.", "adminUsers.changePassword.errors.WrongOldPassword.description": "<PERSON><PERSON> sa<PERSON> on väärä.", "adminUsers.changePassword.errors.default.description": "Salasanan vaihto epäonnistui.", "adminUsers.returnToEvents": "<PERSON><PERSON><PERSON>", "initialSetup.title": "<PERSON><PERSON>", "initialSetup.welcome1": "Tervetuloa k<PERSON>tämään Ilmomasiinaa!", "initialSetup.welcome2": "Aloita luomalla ensimmäinen hallintakäyttäjä. Voit luoda lisää käyttäjiä my<PERSON><PERSON><PERSON> hallintapaneelin kautta.", "initialSetup.email": "Sähköposti", "initialSetup.password": "<PERSON><PERSON><PERSON>", "initialSetup.passwordVerify": "<PERSON><PERSON><PERSON>", "initialSetup.submit": "<PERSON><PERSON>", "initialSetup.errors.required": "Pakollinen kenttä", "initialSetup.errors.minLength": "Salasanassa täytyy olla vähintään {{number}} merkkiä", "initialSetup.errors.verifyMatch": "Salasanat eivät täsmää", "initialSetup.success": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> luotiin onnist<PERSON>est<PERSON>.", "initialSetup.errors.InitialSetupAlreadyDone.description": "Ensimmäinen käyttäjä on jo luotu. <PERSON><PERSON><PERSON><PERSON><PERSON> sisään olemassaolevalla käyttäjällä.", "initialSetup.errors.default.description": "Käyttäjä<PERSON> luonti ep<PERSON>.", "auditLog.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "auditLog.loadError.default.description": "<PERSON>en lataus epäonnistui. $t(errors.contactAdmin)", "auditLog.returnToEvents": "<PERSON><PERSON><PERSON>", "auditLog.column.time": "<PERSON><PERSON>", "auditLog.column.user": "Käyttäjä", "auditLog.column.ipAddress": "IP-osoite", "auditLog.column.action": "<PERSON><PERSON><PERSON><PERSON>", "auditLog.filter": "<PERSON><PERSON><PERSON>…", "auditLog.filter.action": "<PERSON><PERSON><PERSON><PERSON>…", "auditLog.filter.event": "<PERSON><PERSON><PERSON><PERSON>…", "auditLog.filter.signup": "Ilmoittaut<PERSON>nen…", "auditLog.filter.action.createEvent": "Tapahtuma: <PERSON><PERSON>", "auditLog.filter.action.editEvent": "Tapahtuma: <PERSON><PERSON><PERSON><PERSON>", "auditLog.filter.action.publishEvent": "Tapahtuma: <PERSON><PERSON><PERSON>", "auditLog.filter.action.unpublishEvent": "Tapahtuma: Luonnoksek<PERSON>", "auditLog.filter.action.deleteEvent": "Tapahtuma: Poista", "auditLog.filter.action.createSignup": "Ilmoittautuminen: <PERSON><PERSON>", "auditLog.filter.action.editSignup": "Ilmoittautuminen: <PERSON><PERSON><PERSON><PERSON>", "auditLog.filter.action.deleteSignup": "Ilmoittautuminen: Poista", "auditLog.filter.action.promoteSignup": "Ilmoittautuminen: <PERSON><PERSON><PERSON>", "auditLog.filter.action.createUser": "Käyttäjä: <PERSON><PERSON>", "auditLog.filter.action.deleteUser": "Käyttäjä: Poista", "auditLog.filter.action.resetPassword": "Käyttäjä: <PERSON><PERSON><PERSON>", "auditLog.filter.action.changeOwnPassword": "Käyttäjä: <PERSON><PERSON><PERSON><PERSON> oma sa<PERSON>ana", "auditLog.pagination": "Rivit <1 />–{{last}}, yhteensä {{total}}", "auditLog.pagination.previous": "<PERSON><PERSON><PERSON> sivu", "auditLog.pagination.next": "<PERSON><PERSON><PERSON> sivu", "auditLog.description.createEvent": "loi tap<PERSON> <1>{{event}}</1>", "auditLog.description.editEvent": "muokkasi tapah<PERSON>aa <1>{{event}}</1>", "auditLog.description.publishEvent": "j<PERSON><PERSON><PERSON> <1>{{event}}</1>", "auditLog.description.unpublishEvent": "palautti luonn<PERSON><PERSON><PERSON>i tap<PERSON> <1>{{event}}</1>", "auditLog.description.deleteEvent": "poisti tap<PERSON> <1>{{event}}</1>", "auditLog.description.createSignup": "loi ilmon {{signup}} tapahtumassa <3>{{event}}</3>", "auditLog.description.editSignup": "muokkasi ilmoa {{signup}} tapahtumassa <3>{{event}}</3>", "auditLog.description.deleteSignup": "poisti ilmon {{signup}} tapahtumassa <3>{{event}}</3>", "auditLog.description.promoteSignup": "ilmo {{signup}} nousi jonosta tap<PERSON> <3>{{event}}</3>", "auditLog.description.createUser": "<PERSON><PERSON> k<PERSON> {{user}}", "auditLog.description.deleteUser": "poisti k<PERSON> {{user}}", "auditLog.description.resetPassword": "no<PERSON><PERSON> k<PERSON> {{user}} salasanan", "auditLog.description.changeOwnPassword": "v<PERSON><PERSON><PERSON> sa<PERSON>a", "auditLog.description.unknown": "tunt<PERSON><PERSON> toiminto {{action}}", "editor.loadError.404.title": "Tapahtumaa ei lö<PERSON>yt", "editor.loadError.404.description": "Tapahtumaa id:llä {{eventId}} ei löytynyt. Se saattaa olla poistettu.", "editor.loadError.default.description": "Tapahtuman lataaminen epäonnistui. $t(errors.contactAdmin)", "editor.title.edit": "<PERSON><PERSON><PERSON><PERSON> tap<PERSON>", "editor.title.new": "<PERSON><PERSON> uusi tap<PERSON>a", "editor.action.goBack": "<PERSON><PERSON><PERSON>", "editor.status.draft": "Luonnos", "editor.status.published": "Julkaistu", "editor.status.createSuccess": "Tapaht<PERSON> luotiin onnist<PERSON>!", "editor.status.saveSuccess": "<PERSON><PERSON><PERSON><PERSON> tall<PERSON> onnist<PERSON>esti!", "editor.saveInvalid": "<PERSON><PERSON><PERSON><PERSON><PERSON> on virheitä. <PERSON><PERSON><PERSON><PERSON> virheet ja yritä tallentamista uudelleen.", "editor.saveError.default.description": "Tapahtuman päivittäminen epäonnistui.", "editor.action.publish": "Julkai<PERSON>", "editor.action.saveDraft": "<PERSON><PERSON><PERSON>", "editor.action.convertToDraft": "<PERSON><PERSON> l<PERSON>", "editor.action.saveChanges": "<PERSON><PERSON><PERSON>", "editor.tabs.basic": "<PERSON><PERSON><PERSON><PERSON>", "editor.tabs.quotas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "editor.tabs.questions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "editor.tabs.emails": "Vahvistusviestit", "editor.tabs.preview": "Esikatsele", "editor.tabs.signups": "Ilmoittautuneet", "editor.errors.required": "Tämä kenttä on pakollinen", "editor.errors.tooShort": "Syötä vähintään {{minimum}} merkkiä", "editor.errors.tooLong": "Syötä enintään {{maximum}} merkkiä", "editor.errors.tooSmall": "<PERSON><PERSON><PERSON> tulee olla v<PERSON> {{minimum}}", "editor.errors.tooLarge": "<PERSON><PERSON><PERSON> tulee olla en<PERSON> {{maximum}}", "editor.errors.invalidSlug": "URL-osoitteessa voi käyttää vain merkkejä A-Z, a-z, 0-9, - ja _", "editor.errors.dateInverted": "Tapahtuma ei voi päättyä ennen kuin se alkaa", "editor.errors.registrationDateInverted": "Ilmoittautuminen ei voi päättyä ennen kuin se alkaa", "editor.errors.optionsTooLong": "Vastausvaihtoehdot ovat yhteen<PERSON>ä liian pitkät", "editor.errors.generic": "Virheellinen arvo: {{error}}", "editor.basic.name": "<PERSON><PERSON><PERSON><PERSON> nimi", "editor.basic.startDate": "Alkuaika", "editor.basic.endDate": "<PERSON><PERSON><PERSON><PERSON>", "editor.basic.endDate.info": "Tapahtuma näkyy kalenteriviennissä vain, jos sille on asetettu loppua<PERSON>.", "editor.basic.url": "Tapahtuman URL", "editor.basic.url.checking": "<PERSON><PERSON><PERSON><PERSON><PERSON> sa<PERSON>…", "editor.basic.url.free": "URL-osoite vapaa!", "editor.basic.url.reserved": "URL-osoite on jo käytössä tapahtumalla {{event}}", "editor.basic.listed": "<PERSON><PERSON><PERSON><PERSON>", "editor.basic.listed.check": "Näytä tap<PERSON>tum<PERSON>tassa", "editor.basic.listed.info": "Piilotettuihin tapahtumiin pääsee vain URL-osoitteella. Luonnoksena tallennettuja tapahtumia ei voi katsella käyttäjänä riippumatta tästä asetuksesta.", "editor.basic.type": "Tapahtuman tyyppi", "editor.basic.type.onlyEvent": "Tapahtuma ilman ilmoittautumista", "editor.basic.type.eventWithSignup": "Tapahtuma ja ilmoittautuminen", "editor.basic.type.onlySignup": "Ilmoittautuminen ilman <PERSON>", "editor.basic.category": "Kategoria", "editor.basic.homePage": "Kotisivujen o<PERSON>ite", "editor.basic.facebook": "Facebook-tapahtuma", "editor.basic.location": "<PERSON><PERSON><PERSON>", "editor.basic.price": "<PERSON><PERSON>", "editor.basic.description": "<PERSON><PERSON><PERSON>", "editor.basic.description.info": "Kuvauksessa voi käyttää Markdownia.", "editor.basic.registrationStartDate": "<PERSON><PERSON> alka<PERSON>", "editor.basic.registrationEndDate": "<PERSON><PERSON>", "editor.basic.signupsPublic": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>", "editor.basic.signupsPublic.check": "Ilmoittautumiset ovat julkisia", "editor.basic.signupsPublic.info": "Ilmoittautuja saa päättää, näkyykö hänen nimensä julkisesti. Vastaukset julkisiin kysymyksiin ovat aina julkisia.", "editor.quotas.addQuota": "Lisää kiintiö", "editor.quotas.deleteQuota": "Poista kiintiö", "editor.quotas.quotaName": "<PERSON><PERSON><PERSON><PERSON><PERSON> nimi", "editor.quotas.quotaName.singleQuota": "<PERSON><PERSON> on vain yksi, voit antaa sen nimeksi esim. tapahtuman nimen.", "editor.quotas.quotaName.reorder": "Voit järjestellä kiintiöitä raaha<PERSON>lla niitä vasemmalta.", "editor.quotas.quotaSize": "<PERSON><PERSON><PERSON><PERSON><PERSON> koko", "editor.quotas.quotaSize.info": "<PERSON><PERSON> k<PERSON> kokoa ei ole r<PERSON>, j<PERSON><PERSON> kenttä tyhj<PERSON>ksi.", "editor.quotas.quotaSize.unlimited": "r<PERSON><PERSON>", "editor.quotas.openQuota": "<PERSON><PERSON>in kii<PERSON>", "editor.quotas.openQuota.check": "Käytä lisäksi yhteistä kiintiötä", "editor.quotas.openQuota.info": "Avoimeen kiintiöön sijoitetaan automaattisesti ilmoittautumisjärjestyksessä ensimmäiset ilmoittautujat, jotka eivät mahdu valitsemaansa kiintiöön.", "editor.quotas.openQuotaSize": "Avoimen kiintiön koko", "editor.questions.nameQuestion": "<PERSON><PERSON>", "editor.questions.nameQuestion.check": "<PERSON><PERSON><PERSON><PERSON> nimet", "editor.questions.nameQuestion.infoOn": "<PERSON><PERSON> on pakollinen kysymys. Osallistuja saa päättää, näkyykö nimi julkisesti.", "editor.questions.nameQuestion.infoOff": "<PERSON><PERSON> ni<PERSON>, osallist<PERSON>ja saa päättää, näkyykö nimi jul<PERSON>i.", "editor.questions.emailQuestion": "Sähköposti", "editor.questions.emailQuestion.check": "Kerää sähköpostiosoitteet", "editor.questions.emailQuestion.infoOn": "Sähköpostiosoite on pakollinen kysymys. Osallistujille lähetetään vahvistussähköposti ja sähköposti-ilmoitus jonosijalta pääsemisestä.", "editor.questions.emailQuestion.infoOff": "<PERSON><PERSON> s<PERSON>köpostiosoitetta ei kysytä, osallistujat eivät saa vahvistussähköpostia tai sähköposti-ilmoitusta jonosijalta pääsemisestä.", "editor.questions.addQuestion": "Lisää kysymys", "editor.questions.deleteQuestion": "Poista k<PERSON>ymys", "editor.questions.questionText": "<PERSON><PERSON><PERSON><PERSON>", "editor.questions.questionType": "Tyyppi", "editor.questions.questionType.text": "Teksti (lyhyt)", "editor.questions.questionType.textarea": "<PERSON><PERSON><PERSON> (pitkä)", "editor.questions.questionType.number": "Numero", "editor.questions.questionType.select": "Monivalinta (voi valita yhden)", "editor.questions.questionType.checkbox": "Monivalinta (voi valita monta)", "editor.questions.questionRequired": "<PERSON><PERSON><PERSON>", "editor.questions.questionPublic": "<PERSON><PERSON><PERSON>", "editor.questions.questionOptions": "Vastausva<PERSON><PERSON>ehto", "editor.questions.questionOptions.add": "Lisää <PERSON>", "editor.questions.questionOptions.delete": "Poista", "editor.emails.verificationEmail": "Vahvistusviesti sähköpostiin", "editor.signups.noSignups": "Tapahtumaan ei vielä ole yhtään ilmoittautumista. Kun tapahtumaan tulee ilmoittautumisia, näet ne tästä.", "editor.signups.emptyQuota": "Kiintiöön ei vielä ole yhtään ilmoittautumista. Kun kiintiöön tulee ilmoittautumisia, näet ne tästä.", "editor.signups.download": "La<PERSON>a <PERSON>", "editor.signups.download.filename": "{{event}} osallistujalista.csv", "editor.signups.groupByQuota": "Näytä kiintiöittäin", "editor.signups.inQueue": "<PERSON><PERSON>", "editor.signups.column.firstName": "<PERSON><PERSON><PERSON><PERSON>", "editor.signups.column.lastName": "<PERSON><PERSON><PERSON><PERSON>", "editor.signups.column.email": "Sähköposti", "editor.signups.column.quota": "Kiintiö", "editor.signups.column.status.in-open": "Avoin", "editor.signups.column.status.in-queue": "<PERSON><PERSON>", "editor.signups.column.time": "Ilmoittautumisaika", "editor.signups.column.actions": "<PERSON><PERSON><PERSON><PERSON>", "editor.signups.action.edit": "<PERSON><PERSON><PERSON><PERSON>", "editor.signups.action.delete": "Poista", "editor.signups.action.delete.confirm": "<PERSON><PERSON><PERSON> var<PERSON>? Poistamista ei voi perua.", "editor.signups.action.create": "Lisää ilmoittautuminen", "editor.signups.unconfirmed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "editor.editConflict.title": "Päällekkä<PERSON> muokkaus", "editor.editConflict.info1": "<PERSON><PERSON> käyttäjä tai vä<PERSON><PERSON>hti on muokannut tätä tapahtumaa <1>{{time}}</1>.", "editor.editConflict.info1.withDeleted": "<PERSON><PERSON> käyttäjä tai väli<PERSON>hti on muokannut tätä tapahtumaa <1>{{time}}</1> ja poistanut seuraavat kiintiöt tai kysymykset:", "editor.editConflict.question": "<PERSON><PERSON><PERSON><PERSON>:", "editor.editConflict.quota": "Kiintiö:", "editor.editConflict.info2": "Voit tallentaa tapahtuman ja ylikirjoittaa toisen käyttäjän muutokset, tai hylätä tekemäsi muutokset ja jatkaa toisen käyttäjän muokkaamasta tapahtumasta.", "editor.editConflict.action.cancel": "Peruuta", "editor.editConflict.action.revert": "Hylkää muutokset", "editor.editConflict.action.overwrite": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "editor.moveToQueue.title": "Siirretäänkö ilmoittautumisia jonoon?", "editor.moveToQueue.info1": "Tekemäsi muutokset kiintiöihin siirtävät vähintään {{number}} jo kiintiöön päässyttä ilmoittautumista jonoon. Käyttäjille ei ilmoiteta tästä automaattisesti.", "editor.moveToQueue.info2": "<PERSON><PERSON><PERSON><PERSON> var<PERSON> jat<PERSON>?", "editor.moveToQueue.action.proceed": "Jatka", "editor.moveToQueue.action.cancel": "Peruuta", "editor.editSignup.title.edit": "Muok<PERSON>a ilmoittautumista", "editor.editSignup.title.create": "<PERSON><PERSON>", "editor.editSignup.quota": "Kiintiö", "editor.editSignup.sendEmail": "Lähetä vahvistus", "editor.editSignup.sendEmail.edit": "Lähetä osallistujalle muokkausvahvistus sähköpostiin", "editor.editSignup.sendEmail.create": "Lähetä osallistujalle ilmoittautumisvahvistus sähköpostiin", "editor.editSignup.keepEditing": "Jatka luontia", "editor.editSignup.keepEditing.check": "Luo lisää ilmoittautumisia tallentamisen jälkeen", "editor.editSignup.validation": "Tämä lomake ohittaa vastausten validoinnin. Tarkista syöttämäsi vastaukset huolellisesti.", "editor.editSignup.action.cancel": "Peruuta", "editor.editSignup.action.save": "<PERSON><PERSON><PERSON>", "editor.editSignup.error.NoSuchSignup.description": "Ilmoittautumista ei lö<PERSON>. Se saattaa olla jo poistettu.", "editor.editSignup.error.default.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tallentaminen epäonnistui.", "editor.editSignup.success": "Ilmoittautuminen tallennettu."}}