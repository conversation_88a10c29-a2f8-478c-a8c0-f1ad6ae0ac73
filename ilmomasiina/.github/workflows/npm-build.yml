name: Publish JS packages

on:
  workflow_call:

jobs:
  npm:
    runs-on: ubuntu-latest
    permissions:
      packages: write
      contents: read
    steps:
      - name: Check out the repo
        uses: actions/checkout@v4

      - uses: pnpm/action-setup@v4

      - name: Setup Node.js for NPM
        uses: actions/setup-node@v4
        with:
          registry-url: "https://registry.npmjs.org"
          node-version-file: ".nvmrc"
          cache: "pnpm"

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Build packages
        run: |
          pnpm run --filter @tietokilta/ilmomasiina-models build
          pnpm run --filter @tietokilta/ilmomasiina-components build

      - name: Publish to NPM
        run: |
          pnpm publish --filter @tietokilta/ilmomasiina-models --no-git-checks --access public
          pnpm publish --filter @tietokilta/ilmomasiina-components --no-git-checks --access public
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}

      - name: Setup Node.js for GitHub Packages
        uses: actions/setup-node@v4
        with:
          registry-url: "https://npm.pkg.github.com"

      - name: Publish to GitHub Packages
        run: |
          pnpm publish --filter @tietokilta/ilmomasiina-models --no-git-checks --access public
          pnpm publish --filter @tietokilta/ilmomasiina-components --no-git-checks --access public
        env:
          NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
